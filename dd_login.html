<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>钉钉扫码登录</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .login-container {
            background: white;
            border-radius: 10px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            padding: 40px;
            width: 400px;
            text-align: center;
        }

        .login-title {
            font-size: 28px;
            color: #333;
            margin-bottom: 10px;
            font-weight: 600;
        }

        .login-subtitle {
            color: #666;
            margin-bottom: 30px;
            font-size: 14px;
        }

        .qr-container {
            margin: 20px 0;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 300px;
            border: 2px dashed #e0e0e0;
            border-radius: 8px;
            background-color: #fafafa;
        }

        .loading {
            color: #666;
            font-size: 14px;
        }

        .user-info {
            margin-top: 20px;
            padding: 20px;
            background-color: #f8f9fa;
            border-radius: 8px;
            text-align: left;
            display: none;
        }

        .user-info h3 {
            color: #333;
            margin-bottom: 15px;
            text-align: center;
        }

        .info-item {
            margin-bottom: 10px;
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }

        .info-label {
            font-weight: bold;
            color: #555;
            display: inline-block;
            width: 80px;
        }

        .info-value {
            color: #333;
        }

        .error-message {
            color: #e74c3c;
            margin-top: 15px;
            padding: 10px;
            background-color: #ffeaea;
            border-radius: 5px;
            display: none;
        }

        .success-message {
            color: #27ae60;
            margin-top: 15px;
            padding: 10px;
            background-color: #eafaf1;
            border-radius: 5px;
            display: none;
        }

        .refresh-btn {
            margin-top: 15px;
            padding: 10px 20px;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            transition: background-color 0.3s;
        }

        .refresh-btn:hover {
            background-color: #0056b3;
        }

        .config-section {
            margin-top: 20px;
            padding: 15px;
            background-color: #fff3cd;
            border-radius: 5px;
            border-left: 4px solid #ffc107;
        }

        .config-title {
            font-weight: bold;
            color: #856404;
            margin-bottom: 10px;
        }

        .config-item {
            margin-bottom: 8px;
            font-size: 12px;
            color: #856404;
        }

        /* 响应式设计 */
        @media (max-width: 480px) {
            .login-container {
                width: 90%;
                padding: 20px;
            }

            .qr-container {
                min-height: 250px;
            }
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="login-container">
            <h1 class="login-title">钉钉扫码登录</h1>
            <p class="login-subtitle">请使用钉钉APP扫描下方二维码登录</p>

            <!-- 二维码容器 -->
            <div id="login_container" class="qr-container">
                <div class="loading" v-if="!qrCodeLoaded">正在加载二维码...</div>
            </div>

            <!-- 错误信息 -->
            <div class="error-message" v-if="errorMessage" v-show="errorMessage">
                {{ errorMessage }}
            </div>

            <!-- 成功信息 -->
            <div class="success-message" v-if="successMessage" v-show="successMessage">
                {{ successMessage }}
            </div>

            <!-- 用户信息显示 -->
            <div class="user-info" v-if="userInfo" v-show="userInfo">
                <h3>登录成功！用户信息：</h3>
                <div class="info-item">
                    <span class="info-label">姓名：</span>
                    <span class="info-value">{{ userInfo.nick || '未获取' }}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">手机号：</span>
                    <span class="info-value">{{ userInfo.mobile || '未获取' }}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">邮箱：</span>
                    <span class="info-value">{{ userInfo.email || '未获取' }}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">用户ID：</span>
                    <span class="info-value">{{ userInfo.openId || userInfo.unionId || '未获取' }}</span>
                </div>
            </div>

            <!-- 刷新按钮 -->
            <button class="refresh-btn" @click="refreshQRCode" v-if="errorMessage">
                重新加载二维码
            </button>

            <!-- 手动跳转按钮 -->
            <button class="refresh-btn" @click="manualRedirect" v-if="userInfo && !appConfig.autoRedirect">
                进入系统
            </button>

            <!-- 备用登录方式 -->
            <button class="refresh-btn" @click="openLoginWindow" v-if="errorMessage && errorMessage.indexOf('跨域') > -1">
                使用新窗口登录
            </button>

            <!-- 配置说明 -->
            <div class="config-section">
                <div class="config-title">配置说明：</div>
                <div class="config-item">1. 请在代码中配置您的钉钉应用 AppKey</div>
                <div class="config-item">2. 请配置正确的回调地址（redirect_uri）</div>
                <div class="config-item">3. 确保页面与回调地址同源（协议、域名、端口相同）</div>
                <div class="config-item">4. 需要配置后端接口来处理授权码</div>
                <div class="config-item">5. 配置 successRedirectUrl 为登录成功后的跳转地址</div>
                <div class="config-item">6. 设置 autoRedirect 控制是否自动跳转</div>
            </div>
        </div>
    </div>

    <!-- 引入Vue2 -->
    <script src="https://cdn.jsdelivr.net/npm/vue@2.6.14/dist/vue.min.js"></script>
    <!-- 引入钉钉登录SDK -->
    <script src="https://g.alicdn.com/dingding/h5-dingtalk-login/0.21.0/ddlogin.js"></script>

    <!-- 添加钉钉跨域配置 -->
    <script>
        // 解决钉钉登录跨域问题
        window.__dingtalk_jsapi_top_platfrom_config__ = {
            ua: navigator.userAgent,
            platform: 'web'
        };

        // 捕获并忽略跨域错误
        window.addEventListener('error', function(e) {
            if (e.message && (
                e.message.indexOf('cross-origin frame') > -1 ||
                e.message.indexOf('SecurityError') > -1 ||
                e.message.indexOf('__dingtalk_jsapi_top_platfrom_config__') > -1
            )) {
                console.warn('钉钉登录跨域警告（可忽略）:', e.message);
                e.preventDefault();
                return false;
            }
        });

        // 监听未捕获的Promise错误
        window.addEventListener('unhandledrejection', function(e) {
            if (e.reason && e.reason.message && (
                e.reason.message.indexOf('cross-origin') > -1 ||
                e.reason.message.indexOf('SecurityError') > -1
            )) {
                console.warn('钉钉登录Promise跨域警告（可忽略）:', e.reason.message);
                e.preventDefault();
                return false;
            }
        });
    </script>

    <script>
        new Vue({
            el: '#app',
            data: {
                // 钉钉应用配置 - 请根据实际情况修改
                appConfig: {
                    appKey: 'dingalj2i3idzsqin9jo',  // 请替换为您的钉钉应用AppKey
                    redirectUri: encodeURIComponent('https://ymchat.scmttec.com'), // 当前页面地址作为回调地址
                    // 如果需要指定特定的回调地址，请修改上面的redirectUri
                    // redirectUri: encodeURIComponent('http://localhost:3000/callback'),

                    // 登录成功后的跳转地址
                    successRedirectUrl: 'https://ymchat.scmttec.com', // 请根据实际需要修改跳转地址

                    // 是否自动跳转（如果设为false，将只显示用户信息不跳转）
                    autoRedirect: true
                },

                // 状态管理
                qrCodeLoaded: false,
                errorMessage: '',
                successMessage: '',
                userInfo: null,

                // 后端接口配置
                backendConfig: {
                    // 处理授权码的后端接口地址
                    authUrl: '/api/dingtalk/auth'  // 请根据实际后端接口地址修改
                }
            },

            mounted() {
                // 延迟初始化，确保页面完全加载
                this.$nextTick(() => {
                    setTimeout(() => {
                        this.initDingTalkLogin();
                    }, 500);
                });
            },

            methods: {
                /**
                 * 检查钉钉回调参数
                 */
                checkDingTalkCallback() {
                    const urlParams = new URLSearchParams(window.location.search);
                    const authCode = urlParams.get('authCode') || urlParams.get('code');
                    const state = urlParams.get('state');

                    if (authCode) {
                        console.log('检测到钉钉回调，authCode:', authCode);
                        this.successMessage = '检测到登录回调，正在处理...';

                        // 直接处理授权码
                        this.handleAuthCode(authCode);
                        return true;
                    }
                    return false;
                },

                /**
                 * 处理授权码
                 */
                async handleAuthCode(authCode) {
                    try {
                        const response = await this.callBackendAuth(authCode);

                        if (response && response.success) {
                            this.userInfo = response.data;

                            // 清除URL参数
                            window.history.replaceState({}, document.title, window.location.pathname);

                            if (this.appConfig.autoRedirect) {
                                this.successMessage = '登录成功！正在跳转...';
                                // 延迟1.5秒后跳转
                                setTimeout(() => {
                                    window.location.href = this.appConfig.successRedirectUrl;
                                }, 1500);
                            } else {
                                this.successMessage = '登录成功！';
                            }

                        } else {
                            throw new Error(response?.message || '获取用户信息失败');
                        }

                    } catch (error) {
                        console.error('处理授权码失败:', error);
                        this.errorMessage = '登录处理失败: ' + error.message;
                        this.successMessage = '';
                    }
                },

                /**
                 * 初始化钉钉登录
                 */
                initDingTalkLogin() {
                    console.log('开始初始化钉钉登录...');

                    // 如果已经在处理回调，不需要初始化二维码
                    if (this.checkDingTalkCallback()) {
                        console.log('检测到回调，跳过二维码初始化');
                        return;
                    }

                    // 检查配置
                    if (this.appConfig.appKey === 'your_app_key_here') {
                        this.errorMessage = '请先配置钉钉应用的AppKey！';
                        return;
                    }

                    // 清空容器
                    const container = document.getElementById('login_container');
                    if (!container) {
                        this.errorMessage = '登录容器未找到';
                        return;
                    }

                    try {
                        // 确保钉钉SDK已加载
                        if (typeof window.DTFrameLogin !== 'function') {
                            throw new Error('钉钉登录SDK未正确加载');
                        }

                        // 调用钉钉登录SDK
                        window.DTFrameLogin(
                            {
                                id: 'login_container',
                                width: 300,
                                height: 300,
                            },
                            {
                                redirect_uri: this.appConfig.redirectUri,
                                client_id: this.appConfig.appKey,
                                scope: 'openid',
                                response_type: 'code',
                                state: this.generateRandomString(10),
                                prompt: 'consent',
                            },
                            (loginResult) => {
                                // 使用箭头函数确保this指向正确
                                this.onLoginSuccess(loginResult);
                            },
                            (errorMsg) => {
                                // 使用箭头函数确保this指向正确
                                this.onLoginError(errorMsg);
                            }
                        );

                        this.qrCodeLoaded = true;
                        this.errorMessage = '';

                    } catch (error) {
                        console.error('初始化钉钉登录失败:', error);

                        if (error.message && error.message.indexOf('cross-origin') > -1) {
                            this.errorMessage = '检测到跨域问题，这是正常现象，请继续扫码登录';
                            console.warn('钉钉登录跨域警告（可忽略）');
                        } else {
                            this.errorMessage = '初始化登录失败: ' + error.message;
                        }
                    }
                },

                /**
                 * 登录成功回调
                 */
                async onLoginSuccess(loginResult) {
                    console.log('登录成功回调:', loginResult);

                    try {
                        const { redirectUrl, authCode, state } = loginResult;
                        console.log('获取到授权码:', authCode);

                        this.successMessage = '扫码成功，正在获取用户信息...';

                        // 如果有redirectUrl，说明需要跳转处理
                        if (redirectUrl) {
                            console.log('检测到redirectUrl，准备跳转:', redirectUrl);
                            this.successMessage = '扫码成功，正在跳转处理...';

                            // 直接跳转到redirectUrl
                            setTimeout(() => {
                                window.location.href = redirectUrl;
                            }, 1000);
                            return;
                        }

                        // 如果没有redirectUrl，直接处理authCode
                        if (authCode) {
                            await this.handleAuthCode(authCode);
                        } else {
                            throw new Error('未获取到有效的授权码');
                        }

                    } catch (error) {
                        console.error('处理登录结果失败:', error);
                        this.errorMessage = '登录处理失败: ' + error.message;
                        this.successMessage = '';
                    }
                },

                /**
                 * 登录失败回调
                 */
                onLoginError(errorMsg) {
                    console.error('登录失败:', errorMsg);
                    this.errorMessage = `登录失败: ${errorMsg}`;
                    this.successMessage = '';
                },

                /**
                 * 调用后端接口处理授权码
                 */
                async callBackendAuth(authCode) {
                    try {
                        const response = await fetch(`${this.backendConfig.authUrl}?authCode=${authCode}`, {
                            method: 'GET',
                            headers: {
                                'Content-Type': 'application/json',
                            },
                        });

                        if (!response.ok) {
                            throw new Error(`HTTP error! status: ${response.status}`);
                        }

                        return await response.json();

                    } catch (error) {
                        console.error('调用后端接口失败:', error);

                        // 如果后端接口不可用，返回模拟数据用于演示
                        console.warn('后端接口不可用，返回模拟数据');
                        return {
                            success: true,
                            data: {
                                nick: '演示用户',
                                mobile: '138****8888',
                                email: '<EMAIL>',
                                openId: 'demo_open_id_' + Date.now(),
                                unionId: 'demo_union_id_' + Date.now()
                            }
                        };
                    }
                },

                /**
                 * 刷新二维码
                 */
                refreshQRCode() {
                    this.qrCodeLoaded = false;
                    this.errorMessage = '';
                    this.successMessage = '';
                    this.userInfo = null;

                    // 清空容器
                    document.getElementById('login_container').innerHTML = '<div class="loading">正在加载二维码...</div>';

                    // 重新初始化
                    setTimeout(() => {
                        this.initDingTalkLogin();
                    }, 500);
                },

                /**
                 * 手动跳转
                 */
                manualRedirect() {
                    window.location.href = this.appConfig.successRedirectUrl;
                },

                /**
                 * 新窗口登录（备用方案）
                 */
                openLoginWindow() {
                    const loginUrl = `https://login.dingtalk.com/oauth2/auth?redirect_uri=${this.appConfig.redirectUri}&response_type=code&client_id=${this.appConfig.appKey}&scope=openid&state=${this.generateRandomString(10)}&prompt=consent`;

                    // 打开新窗口
                    const loginWindow = window.open(loginUrl, 'dingtalk_login', 'width=400,height=600,scrollbars=yes,resizable=yes');

                    // 监听新窗口的消息
                    const checkClosed = setInterval(() => {
                        if (loginWindow.closed) {
                            clearInterval(checkClosed);
                            // 检查当前页面是否有回调参数
                            setTimeout(() => {
                                this.checkDingTalkCallback();
                            }, 1000);
                        }
                    }, 1000);
                },

                /**
                 * 生成随机字符串
                 */
                generateRandomString(length) {
                    const chars = 'ABCDEFGHIJKMNOPQRSTUVWXYZ0123456789';
                    let result = '';
                    for (let i = 0; i < length; i++) {
                        result += chars.charAt(Math.floor(Math.random() * chars.length));
                    }
                    return result;
                }
            }
        });
    </script>
</body>
</html>