<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="shortcut icon" href="https://img.scmttec.com/gw/laiyuemiao/<EMAIL>">
    <title>约苗小工具</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.62.0/codemirror.min.css" />
    <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.62.0/codemirror.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.62.0/mode/yaml/yaml.min.js"></script>
    <!-- 添加了 js-yaml 库的导入 -->
    <!-- <script src="https://cdn.bootcdn.net/ajax/libs/js-yaml/4.1.0/js-yaml.min.js"></script> -->
    <!-- 引入vue -->
    <script src="./lib/js/vue.min.js"></script>
    <!-- 引入axios-->
    <script src="./lib/js/axios.min.js"></script>
    <!-- 引入element-ui@2.15.6样式表 -->
    <link rel="stylesheet" href="https://img.scmttec.com/hospital/libs/element-ui2.15.6/theme-chalk/index.min.css">
    <!-- element-ui组件 -->
    <script src="https://img.scmttec.com/hospital/libs/element-ui.min.js"></script>
    <!-- 引入js-base64 -->
    <script src="./lib/js/js-base64-main/base64.js"></script>
    <!-- 引入CryptoJS加密库 -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/crypto-js/4.1.1/crypto-js.min.js"></script>
    <!-- 引入CryptoJS加密库 -->
    <script src="./lib/js/sm-crypto/dist/sm4.js"></script>
    <style>
        :root {
            --primary-color: #409EFF;
            --secondary-color: #67C23A;
            --warning-color: #E6A23C;
            --danger-color: #F56C6C;
            --info-color: #909399;
            --background-color: #f5f7fa;
            --card-background: #ffffff;
            --text-color: #303133;
            --border-radius: 8px;
            --transition-speed: 0.3s;
            --gradient-primary: linear-gradient(135deg, #409EFF, #36D1DC);
            --gradient-success: linear-gradient(135deg, #67C23A, #95D475);
            --gradient-warning: linear-gradient(135deg, #E6A23C, #F3D19E);
            --gradient-danger: linear-gradient(135deg, #F56C6C, #F78989);
            --shadow-sm: 0 2px 4px rgba(0,0,0,0.1);
            --shadow-md: 0 4px 8px rgba(0,0,0,0.1);
            --shadow-lg: 0 8px 16px rgba(0,0,0,0.1);
        }

        body {
            margin: 0;
            padding: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background-color: var(--background-color);
            color: var(--text-color);
        }

        .operation-wrapper {
            width: 100% !important;
            min-height: 100vh;
        }

        .el-header {
            background: var(--gradient-primary);
            line-height: 60px;
            display: flex;
            justify-content: flex-start;
            align-items: center;
            padding: 0 20px;
            box-shadow: var(--shadow-md);
            position: relative;
            z-index: 1000;
        }

        .header-img {
            width: 45px;
            height: 28px;
            margin-right: 10px;
            filter: drop-shadow(var(--shadow-sm));
        }

        .el-main {
            padding: 20px !important;
            height: calc(100vh - 61px);
            overflow-y: auto;
            background-color: var(--background-color);
        }

        .el-tabs__item {
            transition: all var(--transition-speed);
            border-radius: var(--border-radius);
            margin: 5px;
        }

        .el-tabs__item.is-active {
            background-color: var(--primary-color);
            color: white !important;
        }

        .el-card {
            border-radius: var(--border-radius);
            transition: transform var(--transition-speed), box-shadow var(--transition-speed);
            margin-bottom: 20px;
            border: none;
            box-shadow: 0 4px 12px rgba(0,0,0,0.05);
        }

        .el-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 24px rgba(0,0,0,0.1);
        }

        .el-button {
            border-radius: var(--border-radius);
            transition: all var(--transition-speed);
        }

        .el-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }

        .el-input {
            width: 100%;
            max-width: 300px;
        }

        .el-input__inner {
            border-radius: var(--border-radius);
        }

        .input-style, .bt-style, .img-data, .worktable {
            margin: 20px;
        }

        .content {
            margin: 0 0 20px 20px;
        }

        .img-data {
            max-width: 100%;
            max-height: 100%;
            width: auto;
            height: auto;
            margin: 10px;
            border-radius: var(--border-radius);
            transition: transform var(--transition-speed);
        }

        .img-data:hover {
            transform: scale(1.05);
        }

        .img-list {
            display: flex;
            flex-wrap: wrap;
            justify-content: space-around;
            gap: 20px;
        }

        .card-style {
            width: 200px;
            margin-right: 20px;
            background: var(--card-background);
            border-radius: var(--border-radius);
            overflow: hidden;
        }

        .firing-range {
            display: flex;
            flex-wrap: wrap;
            justify-content: flex-start;
            gap: 20px;
        }

        .CodeMirror {
            min-height: 666px;
            border-radius: var(--border-radius);
            overflow: hidden;
        }

        .doris-box {
            margin: 30px;
            background: var(--card-background);
        }

        .doris-content {
            display: flex;
            justify-content: flex-start;
            flex-wrap: wrap;
            gap: 10px;
        }

        .doris-bt {
            margin: 10px;
        }

        .aes-sy {
            color: var(--danger-color);
            font-weight: bold;
            font-size: 20px;
            padding: 20px;
            background: rgba(245,108,108,0.1);
            border-radius: var(--border-radius);
        }

        .ai-box {
            width: 100%;
            height: 100%;
            min-height: 1200px;
        }

        .el-timeline-item__node {
            background-color: var(--primary-color);
        }

        .el-timeline-item__tail {
            border-left: 2px solid var(--primary-color);
        }

        .el-table {
            border-radius: var(--border-radius);
            overflow: hidden;
            box-shadow: var(--shadow-sm);
            background: var(--card-background);
            transition: all var(--transition-speed);
        }

        .el-table:hover {
            box-shadow: var(--shadow-md);
        }

        .el-table th {
            background: var(--gradient-primary) !important;
            color: white !important;
            font-weight: 600;
            padding: 12px 0;
        }

        .el-table td {
            padding: 12px 0;
            transition: all var(--transition-speed);
        }

        .el-table tr:hover td {
            background-color: rgba(64,158,255,0.05) !important;
        }

        .el-backtop {
            background-color: var(--primary-color);
            color: white;
            border-radius: 50%;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }

        .el-backtop:hover {
            background-color: #66b1ff;
        }

        @media (max-height: 1200px) {
            .ai-box {
                min-height: 100vh;
            }
        }

        /* 添加动画效果 */
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .el-card, .el-tab-pane {
            animation: fadeIn 0.5s ease-out;
        }

        /* 添加滚动条样式 */
        ::-webkit-scrollbar {
            width: 8px;
            height: 8px;
        }

        ::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb {
            background: var(--primary-color);
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: #66b1ff;
        }

        .link-wrapper {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 4px 8px;
            border-radius: var(--border-radius);
            transition: all var(--transition-speed);
            background: transparent;
        }

        .link-wrapper:hover {
            background: rgba(64,158,255,0.05);
        }

        .link-wrapper .el-link {
            color: var(--primary-color);
            text-decoration: none;
            font-weight: 500;
            transition: all var(--transition-speed);
            position: relative;
            padding: 4px 0;
        }

        .link-wrapper .el-link:hover {
            color: #66b1ff;
        }

        .link-wrapper .el-link::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 0;
            height: 2px;
            background: var(--gradient-primary);
            transition: width var(--transition-speed);
        }

        .link-wrapper .el-link:hover::after {
            width: 100%;
        }

        .link-wrapper .copy-btn {
            opacity: 0;
            transform: translateX(-10px);
            transition: all var(--transition-speed);
            background: var(--gradient-success);
            border: none;
            color: white;
            padding: 4px 12px;
            font-size: 12px;
            height: 24px;
            line-height: 1;
            border-radius: 12px;
            box-shadow: var(--shadow-sm);
        }

        .link-wrapper:hover .copy-btn {
            opacity: 1;
            transform: translateX(0);
        }

        .link-wrapper .copy-btn:hover {
            box-shadow: var(--shadow-md);
            transform: translateY(-1px);
        }

        .el-table .el-table__cell {
            padding: 8px 0;
            text-align: left;
        }

        .el-table .el-table__header th {
            text-align: left;
            font-size: 14px;
            letter-spacing: 0.5px;
        }

        /* 优化表格内容溢出处理 */
        .el-table .cell {
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
    </style>
</head>
<body>
    <el-row id="app">
        <el-container class="operation-wrapper">
            <el-header>
                <img class="header-img" :src="logo" alt="some_text">
                <h2>测试小工具</h2>
            </el-header>
            <el-main>
                <el-tabs :tab-position="tabPosition">
                    <el-tab-pane label="工作台">
                        <div class="worktable">
                            <h3>AI工具</h3>
                            <div class="firing-range" >
                                <el-card class="card-style" shadow="hover" v-for="item in aiData">
                                    <el-link type="primary" :underline="false" :href="item.url" target="_blank"><img style="font-size: 2em;width: 12px;height: 12px;margin-right: 3px;" :src="item.imageUrl" alt="Image Description">{{item.platformName}}</el-link>
                                    <P v-show="item.isShow">{{item.remark}}</P>        
                                </el-card>
                            </div>                     
                            <h3>安全测试-练习靶场</h3>
                            <div class="firing-range">
                                <el-card class="card-style" shadow="hover" v-for="item in securityData">
                                    <el-link type="primary" :underline="false" :href="item.url" target="_blank">{{item.platformName}}</el-link>
                                    <P v-show="item.isShow" style="font-size: 12px;">{{item.account}}</P>                     
                                </el-card>
                            </div>
                            <h3>测试平台</h3>
                            <div class="firing-range">
                                <el-card class="card-style" shadow="hover" v-for="item in testPlatfromData">
                                    <el-link type="primary" :underline="false" :href="item.url" target="_blank">{{item.platformName}}</el-link>
                                    <P v-show="item.isShow" style="font-size: 12px;">{{item.account}}</P>
                                </el-card>
                            </div>
                            <h3>平台链接</h3>
                            <div class="platform-link">
                                <el-table :data="tableData" style="width: 100%">
                                    <el-table-column prop="platform" label="平台名称" width="180"></el-table-column>
                                    <el-table-column prop="test_url" label="测试">
                                        <template slot-scope="scope">
                                          <div class="link-wrapper">
                                            <el-link type="primary" :underline="false" :href="scope.row.test_url" target="_blank">{{ scope.row.test_url }}</el-link>
                                            <el-button type="success" size="mini" class="copy-btn" @click="copyLink(scope.row.test_url)">复制链接</el-button>
                                          </div>
                                        </template>
                                    </el-table-column>
                                    <el-table-column prop="pre_url" label="预发">
                                        <template slot-scope="scope">
                                          <div class="link-wrapper">
                                            <el-link type="primary" :underline="false" :href="scope.row.pre_url" target="_blank">{{ scope.row.pre_url }}</el-link>
                                            <el-button type="success" size="mini" class="copy-btn" @click="copyLink(scope.row.pre_url)">复制链接</el-button>
                                          </div>
                                        </template>
                                    </el-table-column>
                                    <el-table-column prop="prod_url" label="生产">
                                        <template slot-scope="scope">
                                          <div class="link-wrapper">
                                            <el-link type="primary" :underline="false" :href="scope.row.prod_url" target="_blank">{{ scope.row.prod_url }}</el-link>
                                            <el-button type="success" size="mini" class="copy-btn" @click="copyLink(scope.row.prod_url)">复制链接</el-button>
                                          </div>
                                        </template>
                                    </el-table-column>
                                </el-table>
                            </div>      
                            <h3>APP安装包</h3>
                            <div class="platform-link">
                                <el-table :data="apkPath" style="width: 100%">
                                    <el-table-column prop="platform" label="渠道名称" width="180"></el-table-column>
                                    <el-table-column prop="test_url" label="测试"> 
                                        <template scope="scope">
                                            <el-link type="primary" :underline="false" :href="scope.row.test_url" target="_blank">{{ scope.row.test_url }}</el-link>
                                        </template>
                                    </el-table-column>
                                    <el-table-column prop="pre_url" label="预发">
                                        <template scope="scope">
                                            <el-link type="primary" :underline="false" :href="scope.row.pre_url" target="_blank">{{ scope.row.pre_url }}</el-link>
                                        </template>
                                    </el-table-column>
                                    <el-table-column prop="prod_url" label="生产">
                                        <template scope="scope">
                                            <el-link type="primary" :underline="false" :href="scope.row.prod_url" target="_blank">{{ scope.row.prod_url }}</el-link>
                                        </template>
                                    </el-table-column>
                                </el-table>
                            </div>
                        </div>                       
                    </el-tab-pane>
                    <el-tab-pane label="图片生成">
                        <el-row :gutter="20">
                            <el-col :md="8" :lg="6" :xl="4">
                                <div class="input-style">
                                    <label for="">图片宽度：</label>
                                    <el-input placeholder="请输入图片宽度" v-model="width" clearable></el-input>
                                </div>
                                <div class="input-style">
                                    <label for="">图片高度：</label>
                                    <el-input placeholder="请输入图片高度" v-model="height" clearable></el-input>
                                </div>
                                <div class="input-style">
                                    <label for="">图片数量：</label>
                                    <el-input placeholder="请输入图片数量" v-model="quantity" clearable></el-input>
                                </div>
                                <div class="input-style">
                                    <el-button type="primary" :loading="btStatus" @click="createImg">生成图片</el-button>
                                    <el-button type="danger"  @click="clearData">数据重置</el-button>
                                </div> 
                            </el-col>
                            <el-col :md="16" :lg="18" :xl="20">
                                <h2>图片展示区：</h3>
                                <p style="color: red;font-size: 13px;">温馨提示：由于图片是从远程服务端获取，可能出现加载延迟，请耐心等待哦！</p>
                                <el-divider></el-divider>
                                <div v-if="imgList.length==0" class="img-list">
                                    <el-empty description="这里空空如也！"></el-empty>
                                </div>
                                <div v-else-if="imgList.length>0" class="img-list">
                                    <img :src="img" class="img-data" alt="" v-for="img in imgList">
                                </div>
                            </el-col>
                        </el-row>                                                                    
                    </el-tab-pane>
                    <el-tab-pane label="约苗埋点数据解密">
                      <el-card shadow="hover"  class="encrypt doris-box">
                        <div slot="header">
                            <span><strong>工具说明</strong></span>
                        </div>
                        <div>
                          <pre><span style="color: red;">ANDROID APP请使用AES加解密工具</span></pre>
                          <pre><span style="color: red;">IOS APP、微信公号请使用Base64加解密工具</span></pre>
                        </div>
                        <el-tabs v-model="encryptTool" @tab-click="handleClick">
                          <el-tab-pane label="Base64加解密" name="base64">
                            <div>
                              <el-button class="bt-style" type="primary" @click="encrypt">Base64加密</el-button>
                              <el-button class="bt-style" type="primary" @click="copyToClipboard">解密并复制</el-button>
                              <el-button class="bt-style" type="danger" @click="clsData">数据重置</el-button>
                            </div>
                            <div class="content">
                              <el-row :gutter="20">
                                  <el-col :span="8">
                                      <div>
                                          <p><b>原始加密数据：</b></p>
                                          <el-input :autosize="{ minRows: 13.6, maxRows: 13.6}" style="width: vw;" v-model="inputText" type="textarea" placeholder="请输入加密文本"/>
                                      </div>                     
                                      <div style="margin-top: 20px" v-show="show">
                                          <p><b>原始解密数据：</b></p>     
                                          <el-input :autosize="{ minRows: 30, maxRows: 30}" style="width: vw;" v-model="outputText" type="textarea" placeholder="原数据"/>
                                      </div>
                                  </el-col>
                                  <el-col :span="16">
                                      <div>
                                          <p><b>格式化解密数据：</b></p>
                                          <el-input :autosize="{ minRows: 47, maxRows: 47}" style="width: vw;" v-model="outputJson" type="textarea" placeholder="格式化数据"/>
                                      </div>                                           
                                  </el-col>
                              </el-row> 
                          </div>
                          </el-tab-pane>
                          <el-tab-pane label="AES加解密" name="AES">
                            <div>
                              <el-button class="bt-style" type="primary"@click="aesEncrypt">AES加密</el-button>
                              <el-button class="bt-style" type="primary" @click="aesDecryptAndCopy">解密并复制</el-button>
                              <el-button class="bt-style" type="danger" @click="clsData">数据重置</el-button>
                            </div>
                            <div class="content">
                              <el-row :gutter="20">
                                  <el-col :span="8">
                                      <div>
                                          <p><b>原始加密数据：</b></p>
                                          <el-input :autosize="{ minRows: 13.6, maxRows: 13.6}" style="width: vw;" v-model="inputText" type="textarea" placeholder="请输入加密文本"/>
                                      </div>                     
                                      <div style="margin-top: 20px" v-show="show">
                                          <p><b>原始解密数据：</b></p>     
                                          <el-input :autosize="{ minRows: 30, maxRows: 30}" style="width: vw;" v-model="outputText" type="textarea" placeholder="原数据"/>
                                      </div>
                                  </el-col>
                                  <el-col :span="16">
                                      <div>
                                          <p><b>格式化解密数据：</b></p>
                                          <el-input :autosize="{ minRows: 47, maxRows: 47}" style="width: vw;" v-model="outputJson" type="textarea" placeholder="格式化数据"/>
                                      </div>                                           
                                  </el-col>
                              </el-row> 
                          </div>
                          </el-tab-pane>
                        </el-tabs>
                      </el-card>                         
                    </el-tab-pane>
                    <el-tab-pane label="Doris">
                        <el-card shadow="hover"  class="doris-box">
                            <div slot="header">
                                <span><strong>注意事项</strong></span>
                            </div>
                            <div> 
                                <pre><span style="color: red;">1、相关数据表：tb_tag(标签)  tb_base_tag(标签查询规则)  tb_tag_users(用户标签明细)  tb_user_group(用户分群)  tb_user_group_user(用户分群明细)  tb_user_group_task(用户分群任务)</span></pre>
                                <pre><span style="color: red;">2、新建分群在整十分会自动更新用户分群的数据、历史分群每天凌晨1-2点全量更新</span></pre>
                                <pre><span style="color: red;">3、查看日志路径：tail -f /data/task-{env}/logs/scheduler.log</span></pre>
                                <pre><span style="color: red;">4、若当天标签数据需要重新生成，请手动在tb_tag_users表中删除对应id的标签，tb_tag表中对应的标签的update_time清空，在执行用户标签脚本</span></pre>
                                <pre><span style="color: red;">5、若当天分群数据需要重新生成，清除分群的tb_user_group_task对应任务数据，然后把分群任务设置为手动更新，再执行用户分群脚本</span></pre>              
                            </div>                   
                          </el-card>
                        <el-row :gutter="5">
                            <el-col :span="8">
                              <el-card shadow="hover"  class="doris-box">
                                <div slot="header">
                                    <span><strong>任务列表</strong></span>
                                </div>
                                <h4>测试环境-数据同步</h4>
                                <div class="doris-content">                                  
                                    <el-button type="primary" class="doris-bt" type="text" @click="TestTag">用户标签</el-button>
                                    <el-button type="primary" class="doris-bt" type="text" @click="TestCluster">用户分群</el-button>
                                    <el-button type="primary" class="doris-bt" type="text" @click="TestIncident">数据概览</el-button>
                                    <el-button type="primary" class="doris-bt" type="text" @click="TestOverview">事件统计</el-button>
                                </div> 
                                <el-divider></el-divider>
                                <h4>预发环境-数据同步</h4>
                                <div class="doris-content">                               
                                    <el-button type="danger" class="doris-bt" type="text" @click="PreTag">用户标签</el-button>
                                    <el-button type="danger" class="doris-bt" type="text" @click="PreCluster">用户分群</el-button>
                                    <el-button type="danger" class="doris-bt" type="text" @click="PreIncident">数据概览</el-button>
                                    <el-button type="danger" class="doris-bt" type="text" @click="PreOverview">事件统计</el-button>
                                </div>                               
                              </el-card>
                            </el-col>
                            <el-col :span="16">
                                <el-card shadow="hover"  class="doris-box">
                                    <div slot="header">
                                        <el-badge value="持续更新中...">
                                            <span><strong>Doris常用命令和函数</strong></span>
                                        </el-badge>                                       
                                        <el-link 
                                        type="warning" 
                                        :underline="false" 
                                        :href="dorisLink" 
                                        target="_blank"
                                        style="float: right; padding: 3px 0"
                                        >Doris操作手册
                                        </el-link>
                                    </div>
                                    <el-tabs v-model="funcName">
                                        <el-tab-pane label="命令" name="zero">
                                            <el-table :data="commandList" border style="width: 100%">
                                                <el-table-column prop="cmd" label="命令"></el-table-column>
                                                <el-table-column prop="remark" label="备注"></el-table-column>
                                            </el-table>
                                        </el-tab-pane>
                                        <el-tab-pane label="时间类" name="first">
                                            <el-table :data="timeClass" border style="width: 100%">
                                                <el-table-column prop="functionName" label="函数名" width="150"></el-table-column>
                                                <el-table-column prop="functionUsage" label="用法" width="500"></el-table-column>
                                                <el-table-column prop="functionRemark" label="备注"></el-table-column>
                                            </el-table>
                                        </el-tab-pane>
                                        <el-tab-pane label="聚合类" name="second">
                                            <el-table :data="aggregationClass" border style="width: 100%">
                                                <el-table-column prop="functionName" label="函数名" width="150"></el-table-column>
                                                <el-table-column prop="functionUsage" label="用法" width="500"></el-table-column>
                                                <el-table-column prop="functionRemark" label="备注"></el-table-column>
                                            </el-table>
                                        </el-tab-pane>
                                        <el-tab-pane label="Bitmap类" name="third">
                                            <el-table :data="bitmapClass" border style="width: 100%">
                                                <el-table-column prop="functionName" label="函数名" width="150"></el-table-column>
                                                <el-table-column prop="functionUsage" label="用法" width="500"></el-table-column>
                                                <el-table-column prop="functionRemark" label="备注"></el-table-column>
                                            </el-table>
                                        </el-tab-pane>
                                        <el-tab-pane label="其他类" name="four">
                                            <el-table :data="otherClass" border style="width: 100%">
                                                <el-table-column prop="functionName" label="函数名" width="150"></el-table-column>
                                                <el-table-column prop="functionUsage" label="用法" width="500"></el-table-column>
                                                <el-table-column prop="functionRemark" label="备注"></el-table-column>
                                            </el-table>
                                        </el-tab-pane>
                                        <el-tab-pane label="常用SQL" name="five">
                                            <el-table :data="sqlClass" border style="width: 100%">
                                                <el-table-column prop="sqlName" label="SQL名称" width="150"></el-table-column>
                                                <el-table-column prop="sqlValue" label="SQL语句" width="1000"></el-table-column>
                                                <el-table-column prop="remark" label="备注"></el-table-column>
                                            </el-table>
                                        </el-tab-pane>
                                    </el-tabs>
                                  </el-card>
                              </el-col>
                          </el-row>
                    </el-tab-pane>
                    <el-tab-pane label="身份证生成">
                        <iframe src="https://sfz.fatcarter.cn/" width="100%" height="1200px" frameborder="0" allowfullscreen sandbox="allow-same-origin allow-scripts allow-popups allow-forms"> </iframe>
                    </el-tab-pane>
                    <el-tab-pane label="Base64加解密">
                        <iframe src="https://tools.geekzhan.com/base64/" width="100%" height="1200px" frameborder="0" allowfullscreen sandbox="allow-same-origin allow-scripts allow-popups allow-forms"> </iframe>
                    </el-tab-pane>
                    <el-tab-pane label="SM4加解密">
                        <el-card shadow="hover"  class="sm doris-box">
                            <div slot="header">
                                <span style="font-size: 22px;"><strong>工具说明</strong></span>
                                <div>
                                    <pre><span style="color:red;font-size: 15px;"><br>SM4加解密，用于加/解密tb_user表数据</span></pre>
                                </div>
                            </div>
                            <el-tabs v-model="activeName" @tab-click="handleClick">
                                <div class="content">
                                    <el-row :gutter="20">
                                        <el-col :style="{ width: '45%' }">
                                            <div style="height: 50px;">
                                                <span >待加密/解密的文本:</span>
                                            </div>
                                            <div>
                                                <el-input :autosize="{ minRows: 13.6, maxRows: 13.6}" style="width: vw;" id="sm4input" v-model="sm4inputText" type="textarea" placeholder="请输入加密/解密文本"/>
                                            </div>
                                        </el-col>
                                        <el-col :style="{ width: '10%' } ">
                                            <p><br></p>
                                            <div style="display: flex; justify-content: center;">
                                                <el-button class="bt-style" type="primary" @click="sm4Encoding">SM4加密</el-button>
                                            </div>
                                            <div style="display: flex; justify-content: center;">
                                                <el-button class="bt-style" type="primary" @click="sm4Decoding">SM4解密</el-button>
                                            </div>
                                            <div style="display: flex; justify-content: center;">
                                                <el-button class="bt-style" type="primary" @click="copySm4">复制结果</el-button>
                                            </div>
                                        </el-col>
                                        <el-col :style="{ width: '45%' }">
                                            <div style="height: 50px;">
                                                <span >加密/解密后数据:</span>
                                            </div>
                                            <div>
                                                <el-input :autosize="{ minRows: 13.6, maxRows: 13.6}" style="width: vw;" id="sm4re" v-model="sm4inputText1" type="textarea" />
                                            </div>
                                        </el-col>
                                    </el-row>
                                    <el-row :gutter="20">
                                        <span>
                                        <br>
                                            <img src="data/icon/sm4icon.png"  width="40" height="40">
                                            <a  style="color:RGB(128, 128, 128);font-size: 15px;"  href="https://www.toolhelper.cn/SymmetricEncryption/SM4" target="_blank">对解密结果不满意？点击跳转外部解密网站>></a>
                                        </span>
                                    </el-row>
                                </div>
                            </el-tabs>
                        </el-card>
                    </el-tab-pane>
					<el-tab-pane label="Json格式化">
                        <iframe src="https://www.json.cn/json/jsononline.html" width="100%" height="1200px" frameborder="0" allowfullscreen sandbox="allow-same-origin allow-scripts allow-popups allow-forms"> </iframe>
                    </el-tab-pane>
                    <el-tab-pane label="AES加密解析">
                        <pre class="aes-sy">
    AES加密密码：yuemiao_zW4NcAbR
    填充：pkcs5padding
    字符集：utf8编码（unicode编码）
                        </pre>
                        <iframe src="http://tool.chacuo.net/cryptaes" width="100%" height="1200px" frameborder="0" allowfullscreen sandbox="allow-same-origin allow-scripts allow-popups allow-forms"> </iframe>
                    </el-tab-pane>
                    <el-tab-pane label="URL编码解码">
                       <iframe src="https://tools.geekzhan.com/urlencode/" width="100%" height="1200px" frameborder="0" allowfullscreen sandbox="allow-same-origin allow-scripts allow-popups allow-forms"> </iframe>
                   </el-tab-pane>
                    <el-tab-pane label="时间戳转换器">
                        <iframe src="http://zaixian.pro/chengyucidian" width="100%" height="1200px" frameborder="0" allowfullscreen sandbox="allow-same-origin allow-scripts allow-popups allow-forms"> </iframe>
                    </el-tab-pane>
                    <el-tab-pane label="经纬度查询">
                        <iframe src="http://jingweidu.757dy.com/" width="100%" height="1200px" frameborder="0" allowfullscreen sandbox="allow-same-origin allow-scripts allow-popups allow-forms"> </iframe>
                    </el-tab-pane>       
                    <el-tab-pane label="Cron表达式">
                        <iframe src="http://cron.ciding.cc/" width="100%" height="1200px" frameborder="0" allowfullscreen sandbox="allow-same-origin allow-scripts allow-popups allow-forms"> </iframe>
                    </el-tab-pane>           
                    <el-tab-pane label="其他工具">
                        <el-empty description="需求收集中，有想法的小伙伴，欢迎随时沟通！"></el-empty>
                    </el-tab-pane>
                    <el-tab-pane label="版本更新日志">
                        <div class="block" style="margin: 30px;">
                            <el-timeline>
                                <el-timeline-item v-for="item in logData" :key="item.title" :timestamp="item.time" icon="el-icon-more" type="primary" placement="top">
                                    <el-card shadow="hover">
                                        <h4>{{item.title}}</h4>
                                        <p>{{item.commit_time}}</p>
                                        <p>{{item.commit_content}}</p>
                                    </el-card>
                                </el-timeline-item>
                            </el-timeline>
                        </div>
                    </el-tab-pane>
					<el-backtop target=".operation-wrapper"><i class="el-icon-caret-top"></i></el-backtop>
                </el-tabs>
            </el-main>
        </el-container>
    </div>
    <script>
        let app = new Vue({
            el: "#app",
            data: {
                "tabPosition" :"left",
                "width" :"",
                "height":"",
                "quantity":"",
                "btShow":false,
                "imgList":[],
                "btStatus":false,
                "activeName": '1',
                "jsonStr":"",
                "logo":"https://img.scmttec.com/gw/laiyuemiao/<EMAIL>",
                "dorisLink":"https://doris.incubator.apache.org/zh-CN/docs/gettingStarted/what-is-new",
                funcName: "zero",
                "yamlContent": '',
                "errorOutput": '',
                "inputText": '',
                "sm4inputText1": '',
                "sm4inputText": '',
                "outputText": '',
                inputJson: '',
                outputJson: '',
                "tableData": [],
                "apkPath": [],
                logData: [],
                aiData: [],
                securityData: [],
                testPlatfromData: [],
                commandList: [],
                timeClass: [],
                aggregationClass: [],
                bitmapClass: [],
                sqlClass: [],
                otherClass: [],
                show: true,
                copyButtonIndex: -1,
                copyButtonType: '',
                visible:false,
                testUrl: 'http://192.168.20.246:3301',
                preUrl: 'http://192.168.20.246:3302',
                encryptTool:"base64",
                aesKey: "yuemiao_zW4NcAbR"
            },
            created() {
                this.getPlatformUrl(),
                this.getApkUrl(),
                this.getLogs(),
                this.getAiUrl(),
                this.getSecurityUrl(),
                this.getFunctionList(),
                this.getTestPlatfromUrl()
            },
            mounted() {

            },
            methods:{
                createImg(){
                    if(this.width=="" || this.height=="" || this.quantity==""){
                        this.warningMessage('图片宽度、高度和数量不能为空！');
                    }else{
                        this.imgList = [];
                        this.btStatus = true
                        for( let i = 0 ; i<this.quantity ; i++ ){
                            setTimeout(() => {
                                axios
                                    .get("https://picsum.photos/"+this.width+"/"+this.height)
                                    .then(response =>{
                                        this.btShow=true;
                                        this.imgList.push(response.request.responseURL)
                                        this.successMessage('已生成：'+this.imgList.length+"/"+this.quantity);
                                    })
                            }, 500*i);
                        }
                        setTimeout(() =>{
                            this.btStatus = false
                        }, 3000)
                    }
                },
                getPlatformUrl(){
                    axios.get("./data/platformUrl.json")
                    .then(response => {
                        this.tableData = response.data.data.platformUrl
                    })
                    .catch(error => {
                        console.log(error)
                    })
                },
                getApkUrl(){
                    axios.get("./data/apkUrl.json")
                    .then(response => {
                        this.apkPath = response.data.data.apkUrl
                    })
                    .catch(error => {
                        console.log(error)
                    })
                },
                getLogs(){
                    axios.get("./data/log.json")
                    .then(response => {
                        this.logData = response.data.data.logs
                    })
                    .catch(error => {
                        console.log(error)
                    })
                },
                getAiUrl(){
                    axios.get("./data/ai.json")
                    .then(response => {
                        this.aiData = response.data.data.aiUrl
                    })
                    .catch(error => {
                        console.log(error)
                    })
                },
                getSecurityUrl(){
                    axios.get("./data/security.json")
                    .then(response => {
                        this.securityData = response.data.data.securityUrl
                    })
                    .catch(error => {
                        console.log(error)
                    })
                },
                getTestPlatfromUrl(){
                    axios.get("./data/testPlatform.json")
                    .then(response => {
                        this.testPlatfromData = response.data.data.testPlatfromUrl
                    })
                    .catch(error => {
                        console.log(error)
                    })
                },
                getFunctionList(){
                    axios.get("./data/function.json")
                    .then(response => {
                        const dataAll = response.data.data
                        this.commandList = dataAll.commandList
                        this.timeClass = dataAll.timeClass
                        this.aggregationClass = dataAll.aggregationClass
                        this.bitmapClass = dataAll.bitmapClass
                        this.sqlClass = dataAll.sqlClass
                        this.otherClass = dataAll.otherClass
                    })
                    .catch(error => {
                        console.log(error)
                    })
                },
                clearData(){
                    this.width=""
                    this.height=""
                    this.imgList=[]
                    this.quantity=""
                    this.btShow=false
                    this.infoMessage('数据重置成功！')
                    // location.reload()
                },
                clickFormat(){
                    if(this.jsonStr == ""){
                        this.warningMessage('Json字符串不能为空')
                    }
                    // 1、JSON.parse：把JSON字符串转换为JSON对象
                    // 2、JSON.stringify：把JSON对象 转换为 有缩进的 JSON字符串格式
                    this.jsonStr = JSON.stringify(JSON.parse(this.jsonStr), null, '\t')
                },
                clearFormat(){
                    this.jsonStr = "";
                    this.infoMessage('数据清空成功！')
                },
                onZip(){
                    if (this.jsonStr.substr(0, 1) === '<' && this.jsonStr.substr(-1, 1) === '>') {
                        try{
                            this.jsonStr = vkbeautify.xmlmin(this.jsonStr,true);
                        }catch(e){
                            this.$refs.alert.show('error','JSON解析失败:'+e.message,1000);
                        }
                    }else{
                        try{
                            this.jsonStr = JSON.stringify(JSON.parse(this.jsonStr));
                        }catch(e){
                            this.$refs.alert.show('error','JSON解析失败:'+e.message,1000);
                        }
                    }
                },
                strToHex(key) {
                    let hexKey= '';
                    for (let i = 0; i < key.length; i++) {
                        let code = key.charCodeAt(i);
                        if (code < 16) hex += '0';
                        hexKey += code.toString(16).toUpperCase();
                    }
                    return hexKey;
                },
                hexStringToString(hexStr) {
                    // 确保16进制字符串不包含空格或换行符
                    hexStr = hexStr.replace(/\s+/g, '');
                    // 检查16进制字符串长度是否为偶数
                    if (hexStr.length % 2 !== 0) {
                        throw new Error('Invalid hex string');
                    }
                    // 将16进制字符串转换为普通字符串
                    let str = '';
                    for (let i = 0; i < hexStr.length; i += 2) {
                        // 读取两个16进制字符并转换为对应的字符
                        const byte = parseInt(hexStr.substr(i, 2), 16);
                        if (!isNaN(byte)) {
                            str += String.fromCharCode(byte);
                        }
                    }
                    return str;
                },
                sm4Encoding(){
                    const key ="1234567812345678";
                    const hexk=this.strToHex(key);
                    if(this.sm4inputText==""){
                        this.warningMessage('请输入加密文本！')
                    }else{
                        const re= sm4.encrypt(this.sm4inputText, hexk);//此方法会返回一个16进制字符串
                        document.getElementById('sm4re').value =window.btoa(this.hexStringToString(re));
                        this.successMessage('加密成功');
                        }
                },
                base64ToHex(base64) {
                  const binaryData = atob(base64);
                  const len = binaryData.length;
                  let bytes = new Uint8Array(len);
                  for (let i = 0; i < len; i++) {
                    bytes[i] = binaryData.charCodeAt(i);
                  }
                  let hexString = Array.from(bytes).map(function(byte) {
                    return ('0' + (byte & 0xFF).toString(16)).slice(-2).toUpperCase();
                  }).join('');
                  return hexString;
                },
                sm4Decoding(){
                    const key ="1234567812345678";
                    const hexk=this.strToHex(key);
                    var globalre="";
                    if(this.sm4inputText==""){
                        this.warningMessage('请输入解密文本！');
                        document.getElementById('sm4re').value='';
                    }else{
                    try{
                        hexinput=this.base64ToHex(this.sm4inputText);
                        let ree= sm4.decrypt(hexinput, hexk);
                        globalre=ree;
                    }
                    catch(e){
                        this.errorMessage('解密异常，格式不正确')
                        document.getElementById('sm4re').value='';
                        return;
                    }
                    if (globalre=="") {
                        this.errorMessage('解密结果为空，请输入合法数据')
                        document.getElementById('sm4re').value='';
                        return;
                        //如果未解密出数据也认为是输入的数据格式不正确
                    }
                        document.getElementById('sm4re').value =(globalre);
                        this.successMessage('解密成功');
                        }
                },
                copySm4(){
                    if(document.getElementById('sm4re').value==""){
                            this.warningMessage('不能复制空文本')
                    }
                    else{
                        var copyElement = document.getElementById('sm4re');
                        var tempTextArea = document.createElement('textarea');
                        tempTextArea.value = copyElement.value;
                        document.body.appendChild(tempTextArea);
                        tempTextArea.select();
                        try {
                            var successful = document.execCommand('copy');
                            this.successMessage(successful ? '复制成功' : '复制失败');
                        } catch (err) {
                            this.successMessage('无法复制', err);
                        }
                    document.body.removeChild(tempTextArea);
                    }
                },
                encrypt() {
                    if(this.inputText==""){
                        this.warningMessage('加密文本不能为空！！')
                    }else{
                        this.outputText = Base64.encode(this.inputText);
                        this.successMessage('加密成功');
                    }
                },
                copyToClipboard() {
                    if(this.inputText==""){
                        this.warningMessage('加密文本不能为空！！')
                    }else{
                        this.outputText = Base64.decode(this.inputText);
                        this.inputJson = JSON.parse(this.outputText);
                        this.outputJson = JSON.stringify(this.inputJson, null, 2);
                        const textarea = document.createElement('textarea')
                        textarea.value = this.outputJson
                        document.body.appendChild(textarea)
                        textarea.select()
                        document.execCommand('copy')
                        document.body.removeChild(textarea)
                        this.successMessage('解密并复制成功');
                    }                   
                },
                clsData() {
                    this.inputText = "";
                    this.inputJson = "";
                    this.outputText = "";
                    this.outputJson = "";
                    this.$message('数据重置成功！');
                },
                async requestWithMessage(baseUrl, urlPath, message){
                    try{
                        const response = await axios.get(baseUrl + urlPath);
                        this.successMessage(message);
                    }catch(error){
                        console.log(error);
                    }
                },
                infoMessage(message){
                    this.$message({
                        showClose: true,
                        message: message,
                    })
                },
                successMessage(message){
                    this.$message({
                        showClose: true,
                        message: message,
                        type: 'success'
                    })
                },
                warningMessage(message){
                    this.$message({
                        showClose: true,
                        message: message,
                        type: 'warning'
                    })
                },
                errorMessage(message){
                    this.$message({
                        showClose: true,
                        message: message,
                        type: 'error'
                    })
                },
                TestCluster(){
                    this.confirmAndExecuteOperation(this.testUrl, "/trigger", "Test用户分群同步成功！")              
                },
                TestTag(){
                    this.confirmAndExecuteOperation(this.testUrl, "/tag", "Test用户标签同步成功！")
                },
                TestIncident(){
                    this.confirmAndExecuteOperation(this.testUrl, "/user-trend", "Test数据概览同步成功！")
                },
                TestOverview(){
                    this.confirmAndExecuteOperation(this.testUrl, "/event-statistic", "Test数据概览同步成功！")
                },
                PreCluster(){
                    this.confirmAndExecuteOperation(this.preUrl, "/trigger", "Pre用户分群同步成功！")
                },
                PreTag(){
                    this.confirmAndExecuteOperation(this.preUrl, "/tag", "Pre用户标签同步成功！")
                },
                PreIncident(){
                    this.confirmAndExecuteOperation(this.preUrl, "/user-trend", "Pre数据概览同步成功！")
                },
                PreOverview(){
                    this.confirmAndExecuteOperation(this.preUrl, "/event-statistic", "Pre事件统计同步成功！")
                },
                confirmAndExecuteOperation(baseUrl, urlPath, message) {
                    this.$confirm('是否执行该操作?', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    this.requestWithMessage(baseUrl, urlPath, message)
                    this.successMessage('执行成功');
                }).catch(() => {
                    this.infoMessage('取消执行');        
                    });
                },
                showCopyButton(index, type) {
                    this.copyButtonIndex = index;
                    this.copyButtonType = type;
                },
                hideCopyButton() {
                    this.copyButtonIndex = -1;
                    this.copyButtonType = '';
                },
                copyLink(url) {
                    const el = document.createElement('textarea');
                    el.value = url;
                    document.body.appendChild(el);
                    el.select();
                    document.execCommand('copy');
                    document.body.removeChild(el);
                    this.$message.success('链接已复制到剪贴板');
                },
                aesEncrypt(){
                  if(this.inputText==""){
                        this.warningMessage('加密文本不能为空！！')
                    }else{
                        this.outputText =  CryptoJS.AES.encrypt(this.inputText, this.aesKey).toString();
                        this.successMessage('加密成功');
                    }
                },
                async aesDecryptAndCopy(){
                  if(this.inputText==""){
                        this.warningMessage('加密文本不能为空！！')
                    }else{
                      const key = CryptoJS.enc.Utf8.parse(this.aesKey);
                      try {
                        console.log(this.inputText);
                        const bytes = CryptoJS.AES.decrypt(this.inputText, key, {padding: CryptoJS.pad.Pkcs7,mode: CryptoJS.mode.ECB});
                        this.outputText = bytes.toString(CryptoJS.enc.Utf8);
                        try {
                          this.outputJson = JSON.stringify(JSON.parse(this.outputText), null, 2);
                        } catch (e) {
                          console.error('无法解析为 JSON 格式:', e);
                          this.outputJson = this.outputText;
                        }
                        const textarea = document.createElement('textarea')
                        textarea.value = this.outputJson
                        document.body.appendChild(textarea)
                        textarea.select()
                        document.execCommand('copy')
                        document.body.removeChild(textarea)
                        this.successMessage('解密并复制成功');
                        } catch(e){
                          console.error('解密失败:', e);
                          this.warningMessage('解密失败，请检查输入文本和密钥');
                        }
                    }
                },
                handleClick(tab) {
                  // 清空所有输入文本
                  this.outputText = '';
                  this.outputJson = '';
                  this.show = true;
  }
            }
        })
    </script>
</body>
</html>