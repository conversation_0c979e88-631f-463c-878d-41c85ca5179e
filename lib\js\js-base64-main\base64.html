<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<!-- $Id: base64.html,v 1.1 2009/03/01 22:00:28 dankogai Exp dankogai $ -->
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Demo for base64.js</title>
</head>
<body>
<h1>Demo for base64.js</h1>
<p>$Id: base64.html,v 1.1 2009/03/01 22:00:28 dankogai Exp dankogai $</p>
<table width="640"><tbody>
<tr><th width="50%">Text</th><th>Base64
(URL Safe <input id="encodeURI" type="checkbox" onclick="doit()">)</th></tr>
<tr>
<th><textarea id="srctxt" cols="32" rows="4" onkeyup="doit()">
</textarea></th>
<th><textarea id="base64" cols="32" rows="4" onkeyup="
    $('srctxt').value = Base64.decode(this.value);
    doit();
    if (1 /*@cc_on -1 @*/) $('data').src = 'data:text/plain;base64,' + this.value;
"></textarea></th>
</tr>
<tr><th width="50%">Roundtrip</th><th>iframe w/ data: (no IE)</th></tr>
<tr>
<th><textarea id="roundtrip" cols="32" rows="4" disabled></textarea></th>
<th><iframe id="data" width="80%" height="64"></iframe></th>
</tr>
</tbody></table>


<script src="./base64.js"></script>
<script>
$ = function(id){ return document.getElementById(id) };
function doit(){
    var encoded = Base64[ 
        'encode' + ($('encodeURI').checked ? 'URI' : '')
     ]($('srctxt').value);
    $('base64').value = encoded;
    if (1 /*@cc_on -1 @*/) {
        $('data').src = 'data:text/plain;base64,' 
            + Base64.encode(Base64.decode(encoded));
    }
    $('roundtrip').value = Base64.decode(encoded);
}
</script>
</body>
</html>
