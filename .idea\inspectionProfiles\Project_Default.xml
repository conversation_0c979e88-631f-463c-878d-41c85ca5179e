<component name="InspectionProjectProfileManager">
  <profile version="1.0">
    <option name="myName" value="Project Default" />
    <inspection_tool class="PyPackageRequirementsInspection" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoredPackages">
        <value>
          <list size="9">
            <item index="0" class="java.lang.String" itemvalue="elasticsearch" />
            <item index="1" class="java.lang.String" itemvalue="pytest" />
            <item index="2" class="java.lang.String" itemvalue="PyYAML" />
            <item index="3" class="java.lang.String" itemvalue="jenkins" />
            <item index="4" class="java.lang.String" itemvalue="python-dotenv" />
            <item index="5" class="java.lang.String" itemvalue="PyMySQL" />
            <item index="6" class="java.lang.String" itemvalue="redis" />
            <item index="7" class="java.lang.String" itemvalue="httprunner" />
            <item index="8" class="java.lang.String" itemvalue="openpyxl" />
          </list>
        </value>
      </option>
    </inspection_tool>
    <inspection_tool class="PyPep8NamingInspection" enabled="true" level="WEAK WARNING" enabled_by_default="true">
      <option name="ignoredErrors">
        <list>
          <option value="N802" />
          <option value="N803" />
          <option value="N806" />
          <option value="N801" />
        </list>
      </option>
    </inspection_tool>
  </profile>
</component>