{"name": "dingtalk-qr-login", "version": "1.0.0", "description": "钉钉扫码登录项目 - 基于Vue2和内嵌二维码方式", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "build": "echo 'Build completed'", "test": "echo 'No tests specified'"}, "keywords": ["<PERSON><PERSON><PERSON>", "qr-login", "vue2", "o<PERSON>h", "authentication"], "author": "Your Name", "license": "MIT", "dependencies": {"express": "^4.18.2", "axios": "^1.6.0", "cors": "^2.8.5", "dotenv": "^16.3.1", "crypto": "^1.0.1", "querystring": "^0.2.1"}, "devDependencies": {"nodemon": "^3.0.1"}}