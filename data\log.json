{"data": {"logs": [{"time": "2025/01/03", "title": "SM4加解密", "commit_time": "何静 提交于 2025/01/03 16:23", "commit_content": "功能描述：提供SM4加解密方法"}, {"time": "2024/03/27", "title": "<PERSON>", "commit_time": "赵乔 提交于 2024/03/27 15:22", "commit_content": "功能描述：可视化操作Doris相关任务"}, {"time": "2023/11/23", "title": "Cron表达式", "commit_time": "赵乔 提交于 2023/11/23 16:22", "commit_content": "功能描述：可视化生成cron表达式"}, {"time": "2023/11/10", "title": "经纬度查询", "commit_time": "赵乔 提交于 2023/11/10 10:08", "commit_content": "功能描述：经纬度查询工具"}, {"time": "2023/08/21", "title": "埋点数据解密", "commit_time": "赵乔 提交于 2023/08/21 14:52", "commit_content": "功能描述：一键对数据进行base64加密和解密，当解密内容为json时，将自动格式化输出"}, {"time": "2023/07/11", "title": "YAML格式校验", "commit_time": "赵乔 提交于 2023/07/11 15:50", "commit_content": "功能描述：YAML格式校验工具"}, {"time": "2023/06/06", "title": "工作台", "commit_time": "赵乔 提交于 2023/06/06 16:50", "commit_content": "功能描述：这是一个工具合集，目前有内部链接管理和安全测试靶场入口"}, {"time": "2023/05/04", "title": "Base64加解密", "commit_time": "赵乔 提交于 2023/05/06 16:50", "commit_content": "功能描述：嵌套了一个Base64加解密的网站，可快速对数据进行加解密"}, {"time": "2023/05/04", "title": "时间戳转换器", "commit_time": "赵乔 提交于 2023/05/06 16:50", "commit_content": "功能描述：嵌套了转换时间戳的网站，可快速处理时间格式数据"}, {"time": "2023/05/04", "title": "身份证生成", "commit_time": "赵乔 提交于 2023/05/04 15:00", "commit_content": "功能描述：嵌套了一个生成身份的网站，可快速生成身份证数据"}, {"time": "2023/04/29", "title": "JSON格式化", "commit_time": "赵乔 提交于 2023/04/29 11:46", "commit_content": "功能描述：可视化生成cron表达式"}, {"time": "2023/04/28", "title": "图片小工具", "commit_time": "赵乔 提交于 2023/04/27 14:46", "commit_content": "功能描述：可批量生成不同像素不同尺寸的图片"}]}}