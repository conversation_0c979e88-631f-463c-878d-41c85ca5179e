{"data": {"commandList": [{"cmd": "SHOW CREATE TABLE 数据库名.表名;", "remark": "获取指定表的创建语句（Create Table Statement）。这将展示出创建该表时所使用的完整DDL（数据定义语言）指令，包括表的结构、字段定义、分区信息、存储属性、以及任何其他在创建表时设定的特殊属性或约束条件。"}, {"cmd": "SHOW CREATE ROUTINE LOAD FOR 数据库名.表名;", "remark": "用于显示特定表上已创建的例行加载（Routine Load）任务的详细创建语句的命令。Doris 是一个面向OLAP的MPP（大规模并行处理）分析型数据库，支持高并发的实时分析查询。"}], "timeClass": [{"functionName": "now()", "functionUsage": "select now();", "functionRemark": "获得当前的时间，以Datetime类型返回"}, {"functionName": "to_date()", "functionUsage": "select to_date('2024-04-01 00:00:00');", "functionRemark": "返回 DATETIME 类型中的日期部分。"}, {"functionName": "date_sub()", "functionUsage": "select date_sub('2024-04-01 23:59:59', INTERVAL 2 DAY)", "functionRemark": "从日期减去指定的时间间隔"}], "aggregationClass": [], "bitmapClass": [{"functionName": "bitmap_contains()", "functionUsage": "select bitmap_contains(BITMAP bitmap, BIGINT input);", "functionRemark": "计算输入值是否在Bitmap列中，返回值是Boolean值."}, {"functionName": "bitmap_to_string()", "functionUsage": "select bitmap_to_string(null);", "functionRemark": "将一个bitmap转化成一个逗号分隔的字符串，字符串中包含所有设置的BIT位。输入是null的话会返回null。"}], "otherClass": [{"functionName": "cast()", "functionUsage": "select cast('1234' as int);", "functionRemark": "用于将一种数据类型转换为另一种数据类型，例如将字符串转换为整数、将整数转换为字符串等。"}, {"functionName": "lateral view", "functionUsage": "LATERAL VIEW generator_function ( expression [, ...] ) [ table_identifier ] AS column_identifier [, ...]", "functionRemark": "与生成器函数（例如 EXPLODE）结合使用，将生成包含一个或多个行的虚拟表。 LATERAL VIEW 将行应用于每个原始输出行。generator_function:生成器函数（EXPLODE、EXPLODE_SPLIT 等）。table_identifier: generator_function 的别名，它是可选项。column_identifier:列出列别名 generator_function，它可用于输出行。 列标识符的数目必须与 generator 函数返回的列数匹配。"}, {"functionName": "explode_bitmap()", "functionUsage": "select * from table lateral view explode_bitmap(bitmap)", "functionRemark": "表函数，需配合 lateral view 使用。展开一个bitmap类型。"}], "sqlClass": [{"sqlName": "查询用户关联标签", "sqlValue": "SELECT t2.id,t2.name FROM tb_tag_users t1 INNER JOIN tb_tag t2 ON t2.id=t1.id WHERE BITMAP_CONTAINS(users,?) and t1.day=? and t2.update_time is not null ORDER BY t2.id ASC", "remark": "需替换?为用户id，day为时间，如2023-04-01"}, {"sqlName": "查询标签关联的用户", "sqlValue": "select user_id from tb_tag_users lateral view explode_bitmap(users) temp1 as user_id WHERE id in (1, 2) LIMIT 10", "remark": "id为标签id，如1,2"}, {"sqlName": "查询用户关联分群", "sqlValue": "SELECT t2.id,t2.name FROM tb_user_group_users t1 INNER JOIN tb_user_group t2 ON t2.id=t1.id WHERE BITMAP_CONTAINS(t1.users,?) and t1.day=? and t2.status=1", "remark": "需替换?为用户id，day为时间，如2023-04-01"}, {"sqlName": "查询分群关联的用户", "sqlValue": "select user_id from tb_user_group_users lateral view explode_bitmap(users) temp1 as user_id WHERE id in (1, 2) LIMIT 10", "remark": "id为分群id，如1,2"}]}}