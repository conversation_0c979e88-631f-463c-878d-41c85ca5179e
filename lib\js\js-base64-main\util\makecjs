#!/usr/bin/env node
const header = `
//
// THIS FILE IS AUTOMATICALLY GENERATED! DO NOT EDIT BY HAND!
//
;(function(global, factory) {
    typeof exports === 'object' && typeof module !== 'undefined'
        ? module.exports = factory()
        : typeof define === 'function' && define.amd
        ? define(factory) :
        // cf. https://github.com/dankogai/js-base64/issues/119
        (function() {
            // existing version for noConflict()
            const _Base64 = global.Base64;
            const gBase64 = factory();
            gBase64.noConflict = () => {
                global.Base64 = _Base64;
                return gBase64;
            };
            if (global.Meteor) { // Meteor.js
                Base64 = gBase64;
            }
            global.Base64 = gBase64;
        })();
}((typeof self !== 'undefined' ? self
        : typeof window !== 'undefined' ? window
        : typeof global !== 'undefined' ? global
        : this
), function() {
    'use strict';
`;
const footer = `
    //
    // export Base64 to the namespace
    //
    // ES5 is yet to have Object.assign() that may make transpilers unhappy.
    // gBase64.Base64 = Object.assign({}, gBase64);
    gBase64.Base64 = {};
    Object.keys(gBase64).forEach(k => gBase64.Base64[k] = gBase64[k]);
    return gBase64;
}));
`;

require('fs').readFile(process.argv[2], 'utf8', (err, data) => {
    if (err) {
        return console.log(err);
    }
    const body = data.replace(/\n\/\/ makecjs:CUT \/\/.+$/s, '');
    console.log(`
${header}
${body}
${footer}
`);
});
